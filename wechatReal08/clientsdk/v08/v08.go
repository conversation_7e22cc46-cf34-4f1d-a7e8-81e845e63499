package v08

import (
	"fmt"
	"runtime"
	"sync"
	"wechatdll/clientsdk/dynlib"

	"github.com/lunny/log"
)

var (
	lib    dynlib.DynamicLibrary
	mu     sync.Mutex
	isInit bool = false
)

func init() {
	fmt.Printf("init v08: %v\n", runtime.GOOS)
	var libPath string
	switch runtime.GOOS {
	case "windows":
		libPath = "lib\\v08.dll"
	case "linux":
		libPath = "libv08.so"
	default:
		panic("unsupported platform")
	}
	//libPath = "D:\\v08.dll" //test
	var err error
	lib, err = dynlib.NewLibrary(libPath)
	if err != nil {
		log.Error("Failed to load library: ", err)
	}
}

func Rqtx(md5 string) uint32 {
	if !isInit {
		mu.Lock()
		defer mu.Unlock()
	}
	ret, _, _ := lib.Call("rqtx", md5)
	log.Info("Rqtx returned: ", ret)
	isInit = true
	return uint32(ret)
}

// encode string
func EncodeString(input string, constant uint32, timestamp uint32) string {
	len := uint32(len(input))
	buff := make([]byte, len)
	copy(buff, []byte(input))
	out := make([]byte, len)
	lib.Call("encode_cstr", buff, len, out, constant, timestamp)
	return string(out)
}

// encode uint64
func EncodeUInt64(input uint64, constant uint32, timestamp uint32) uint64 {
	input_v := input
	out := uint64(0)
	lib.Call("encode_int64", &input_v, timestamp, constant, &out)
	return out
}

// config len(config) = 128
func Si(config string, data string) string {
	return ""
	// si_out := make([]byte, 33)
	// lib.Call("si_cstr", []byte(config), len(config), []byte(data), len(data), si_out)
	// return string(si_out)
}
