package wxcore

import (
	"fmt"
	"log"
	"sync"
	"wechatdll/TcpPoll"
	"wechatdll/srv/wxface"
)

// WXConnectMgr 微信连接管理器
type WXConnectMgr struct {
	connections map[string]wxface.IWXConnect // 连接列表
	mu          sync.RWMutex                 // 读写锁
	running     bool                         // 是否运行中
}

// 单例模式
var wxConnectMgrOnce sync.Once
var wxConnectMgrInstance *WXConnectMgr

// GetWXConnectMgr 获取连接管理器单例
func GetWXConnectMgr() *WXConnectMgr {
	wxConnectMgrOnce.Do(func() {
		wxConnectMgrInstance = &WXConnectMgr{
			connections: make(map[string]wxface.IWXConnect),
			running:     true,
		}
		// 设置业务心跳启动器
		TcpPoll.BusinessHeartBeatStarter = wxConnectMgrInstance.AddConnection
	})
	return wxConnectMgrInstance
}

// AddConnection 添加连接
func (mgr *WXConnectMgr) AddConnection(wxid string) error {
	mgr.mu.Lock()
	defer mgr.mu.Unlock()

	// 检查是否已存在
	if _, exists := mgr.connections[wxid]; exists {
		return fmt.Errorf("连接 %s 已存在", wxid)
	}

	// 创建新连接
	conn := NewWXConnect(wxid)
	err := conn.Start()
	if err != nil {
		return err
	}

	mgr.connections[wxid] = conn
	mgr.showConnectionInfo()
	return nil
}

// RemoveConnection 移除连接
func (mgr *WXConnectMgr) RemoveConnection(wxid string) {
	mgr.mu.Lock()
	defer mgr.mu.Unlock()

	if conn, exists := mgr.connections[wxid]; exists {
		conn.Stop()
		delete(mgr.connections, wxid)
		mgr.showConnectionInfo()
	}
}

// GetConnection 获取连接
func (mgr *WXConnectMgr) GetConnection(wxid string) wxface.IWXConnect {
	mgr.mu.RLock()
	defer mgr.mu.RUnlock()
	return mgr.connections[wxid]
}

// showConnectionInfo 显示连接信息
func (mgr *WXConnectMgr) showConnectionInfo() {
	totalNum := len(mgr.connections)
	log.Printf("[WXConnectMgr] 当前微信连接数量: %d", totalNum)
}

// StopAll 停止所有连接
func (mgr *WXConnectMgr) StopAll() {
	mgr.mu.Lock()
	defer mgr.mu.Unlock()

	for wxid, conn := range mgr.connections {
		conn.Stop()
		delete(mgr.connections, wxid)
	}
	mgr.showConnectionInfo()
}

// InitWXConnectMgr 初始化微信连接管理器（在程序启动时调用）
func InitWXConnectMgr() {
	// 初始化管理器，这会自动设置BusinessHeartBeatStarter
	_ = GetWXConnectMgr()
	log.Println("[WXConnectMgr] 微信连接管理器已初始化")
}
