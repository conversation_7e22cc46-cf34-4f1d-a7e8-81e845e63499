package wxcore

import (
	"wechatdll/models"
	"wechatdll/models/Login"
	"wechatdll/srv/wxface"
)

// WXModels 微信业务实现
type WXModels struct {
}

// NewWXModels 创建微信业务实例
func NewWXModels() wxface.IWXModels {
	return &WXModels{}
}

// HeartBeat 心跳接口实现
func (w *WXModels) HeartBeat(wxid string) models.ResponseResult {
	return Login.HeartBeat(wxid)
}

// Secautoauth 二次登录接口实现
func (w *WXModels) Secautoauth(wxid string) models.ResponseResult {
	return Login.Secautoauth(wxid)
}
