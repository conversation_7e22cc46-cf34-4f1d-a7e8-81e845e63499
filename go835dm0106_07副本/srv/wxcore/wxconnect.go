package wxcore

import (
	"errors"
	"fmt"
	"log"
	"time"
	"wechatdll/Cilent/mm"
	"wechatdll/TcpPoll"
	"wechatdll/comm"
	"wechatdll/models"
	"wechatdll/srv/wxface"

	"github.com/astaxie/beego"
)

// WXConnect 微信连接（类似08项目）
type WXConnect struct {
	// 微信业务接口
	wxModels wxface.IWXModels
	// 微信ID
	wxid string
	// 心跳定时器
	HeartBeatTimer *time.Timer
	// 刷新Token定时器
	RefreshTokenTimer *time.Timer
	// 退出信号
	ExitFlagChan chan bool
	// 是否连接中
	isConnected bool
	// 启动时间，避免重复启动
	startTime int64
}

// NewWXConnect 创建微信连接
func NewWXConnect(wxid string) wxface.IWXConnect {
	wxconn := &WXConnect{
		wxid:         wxid,
		ExitFlagChan: make(chan bool, 1),
		isConnected:  false,
	}
	wxconn.wxModels = NewWXModels()
	return wxconn
}

// Start 启动微信连接
func (wxconn *WXConnect) Start() error {
	// 检查用户信息是否存在
	userInfo, err := comm.GetLoginata(wxconn.wxid)
	if err != nil || userInfo == nil || userInfo.Wxid == "" {
		return fmt.Errorf("获取用户信息失败: %v", err)
	}

	// 重置启动时间
	wxconn.startTime = time.Now().Unix()
	wxconn.isConnected = true

	// 初始化定时器
	wxconn.HeartBeatTimer = time.NewTimer(time.Second * 175)
	wxconn.RefreshTokenTimer = time.NewTimer(time.Minute * 1) // 改为1分钟

	// 启动心跳循环
	go wxconn.startLongWriter()

	log.Printf("[WXConnect] 用户 %s 微信连接已启动", wxconn.wxid)
	return nil
}

// Stop 停止微信连接
func (wxconn *WXConnect) Stop() {
	if !wxconn.isConnected {
		return
	}

	wxconn.isConnected = false
	wxconn.startTime = time.Now().Unix() // 重置时间，停止循环

	// 发送退出信号
	select {
	case wxconn.ExitFlagChan <- true:
	default:
	}

	// 停止定时器
	if wxconn.HeartBeatTimer != nil {
		wxconn.HeartBeatTimer.Stop()
	}
	if wxconn.RefreshTokenTimer != nil {
		wxconn.RefreshTokenTimer.Stop()
	}

	log.Printf("[WXConnect] 用户 %s 微信连接已停止", wxconn.wxid)
	// 记录到beego日志（按照08项目模式）
	timeStr := time.Now().Format("2006-01-02 15:04:05")
	beego.Info(fmt.Sprintf("[%s] 退出！ %s", wxconn.wxid, timeStr))
	beego.Info(fmt.Sprintf("[自动心跳] 用户 %s 微信连接已停止，自动心跳和刷新服务已关闭", wxconn.wxid))

	// 停止TCP心跳（按照08项目方式）
	tcpManager, err := TcpPoll.GetTcpManager()
	if err == nil {
		// 获取用户信息
		userInfo, err := comm.GetLoginata(wxconn.wxid)
		if err == nil && userInfo != nil {
			// 获取TCP客户端
			client, err := tcpManager.GetClient(userInfo)
			if err == nil && client != nil {
				beego.Info(fmt.Sprintf("[TCP心跳] 用户 %s 业务层请求停止TCP心跳", wxconn.wxid))
				tcpManager.Remove(client)
				log.Printf("[WXConnect] 用户 %s TCP心跳已停止", wxconn.wxid)
			}
		}
	}
}

// startLongWriter 开启长连接发送数据（类似08项目）
func (wxconn *WXConnect) startLongWriter() {
	startTime := wxconn.startTime

	for {
		select {
		case <-wxconn.HeartBeatTimer.C:
			if startTime != wxconn.startTime || !wxconn.isConnected {
				return
			}
			// 发送心跳包
			_ = wxconn.SendHeartBeat()
			continue

		case <-wxconn.RefreshTokenTimer.C:
			if startTime != wxconn.startTime || !wxconn.isConnected {
				return
			}
			// 刷新Token（从0开始重试）
			_ = wxconn.RefreshTokenWithRetry(0)
			continue

		case <-wxconn.ExitFlagChan:
			return
		}
	}
}

// SendHeartBeat 发送心跳
func (wxconn *WXConnect) SendHeartBeat() error {
	timeStr := time.Now().Format("2006-01-02 15:04:05")

	// 调用心跳接口
	result := wxconn.wxModels.HeartBeat(wxconn.wxid)

	if result.Success {
		// 智能调整心跳间隔时间
		nextTimeSeconds := wxconn.getNextTimeFromResult(result)
		if nextTimeSeconds <= 0 {
			nextTimeSeconds = 175 // 默认175秒
		}

		// 重置心跳定时器
		wxconn.HeartBeatTimer.Reset(time.Duration(nextTimeSeconds) * time.Second)
		nextTime := time.Now().Add(time.Duration(nextTimeSeconds) * time.Second).Format("2006-01-02 15:04:05")
		log.Printf("[WXConnect] %s 心跳成功，智能间隔：%d秒，下次心跳时间：%s", wxconn.wxid, nextTimeSeconds, nextTime)
		return nil
	} else {
		log.Printf("[WXConnect] %s 心跳失败：%s，已停止自动心跳 %s", wxconn.wxid, result.Message, timeStr)
		wxconn.Stop()
		return errors.New("发送心跳失败")
	}
}

// RefreshToken 刷新Token（带重试逻辑，和08项目一致）
func (wxconn *WXConnect) RefreshToken() error {
	return wxconn.RefreshTokenWithRetry(0)
}

// RefreshTokenWithRetry 刷新Token（带重试计数）
func (wxconn *WXConnect) RefreshTokenWithRetry(num int) error {
	timeStr := time.Now().Format("2006-01-02 15:04:05")

	// 获取用户信息检查是否需要刷新
	userInfo, err := comm.GetLoginata(wxconn.wxid)
	if err != nil || userInfo == nil || userInfo.Wxid == "" {
		log.Printf("[WXConnect] %s 获取用户信息失败：%v，已停止自动心跳 %s", wxconn.wxid, err, timeStr)
		wxconn.Stop()
		return errors.New("获取用户信息失败")
	}

	// 检查上次登录时间（30分钟内不重复刷新）
	if userInfo.LoginDate+1800 > time.Now().Unix() {
		minutes := (userInfo.LoginDate + 3600 - time.Now().Unix()) / 60
		if minutes <= 1 {
			minutes = 1
		}
		wxconn.RefreshTokenTimer.Reset(time.Duration(minutes) * time.Minute)
		nextTime := time.Now().Add(time.Duration(minutes) * time.Minute).Format("2006-01-02 15:04:05")
		log.Printf("[WXConnect] %s 自动二次登录已开启，下次刷新时间：%s", wxconn.wxid, nextTime)
		return nil
	}

	// 调用二次登录接口
	result := wxconn.wxModels.Secautoauth(wxconn.wxid)

	// 检查返回结果是否为空（按照08项目逻辑）
	if result.Data == nil {
		log.Printf("[WXConnect] %s 发送二次登录失败: result.Data == nil", wxconn.wxid)
		if num < 3 {
			time.Sleep(time.Second * 10)
			go wxconn.RefreshTokenWithRetry(num + 1)
			return nil
		}
		log.Printf("[WXConnect] %s 二次登录失败：result.Data == nil，已停止自动心跳 %s", wxconn.wxid, timeStr)
		wxconn.Stop()
		return errors.New("result.Data == nil 发送二次登录失败")
	}

	if result.Success {
		// 重置刷新定时器为1小时（按照08项目）
		wxconn.RefreshTokenTimer.Reset(time.Hour)
		nextTime := time.Now().Add(time.Hour).Format("2006-01-02 15:04:05")
		log.Printf("[WXConnect] %s 二次登录成功，下次刷新时间：%s", wxconn.wxid, nextTime)
		return nil
	} else {
		log.Printf("[WXConnect] %s 发送二次登录失败: Success=false", wxconn.wxid)
		if num < 3 {
			time.Sleep(time.Second * 10)
			go wxconn.RefreshTokenWithRetry(num + 1)
			return nil
		}
		log.Printf("[WXConnect] %s 二次登录失败：%s，已停止自动心跳 %s", wxconn.wxid, result.Message, timeStr)
		wxconn.Stop()
		return errors.New("发送二次登录失败")
	}
}

// getNextTimeFromResult 从返回结果中解析NextTime
func (wxconn *WXConnect) getNextTimeFromResult(result models.ResponseResult) int {
	// 尝试从Data中解析NextTime（修复：正确处理HeartBeatResponse结构体）
	if result.Data != nil {
		// 检查是否为HeartBeatResponse类型
		if heartBeatResp, ok := result.Data.(mm.HeartBeatResponse); ok {
			nextTime := heartBeatResp.GetNextTime()
			if nextTime > 0 {
				return int(nextTime)
			}
		}
		// 兼容处理：如果是map类型（向后兼容）
		if dataMap, ok := result.Data.(map[string]interface{}); ok {
			if nextTime, exists := dataMap["NextTime"]; exists {
				switch v := nextTime.(type) {
				case int:
					return v
				case float64:
					return int(v)
				case string:
					// 尝试解析字符串为数字
					var result int
					if num, err := fmt.Sscanf(v, "%d", &result); err == nil && num == 1 {
						return result
					}
				}
			}
		}
	}
	return 175 // 默认175秒
}
