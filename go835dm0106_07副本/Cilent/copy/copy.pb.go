// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.14.0
// source: copy.proto

package copy

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SaeInfo struct {
	Type                 []byte   `protobuf:"bytes,1,opt,name=type" json:"type,omitempty"`
	Iv                   []byte   `protobuf:"bytes,2,opt,name=iv" json:"iv,omitempty"`
	Len                  *uint32  `protobuf:"varint,3,opt,name=len" json:"len,omitempty"`
	UnknowValue9         []byte   `protobuf:"bytes,9,opt,name=unknowValue9" json:"unknowValue9,omitempty"`
	TableKey             []byte   `protobuf:"bytes,10,opt,name=tableKey" json:"tableKey,omitempty"`
	UnknowValue11        []byte   `protobuf:"bytes,11,opt,name=unknowValue11" json:"unknowValue11,omitempty"`
	TableValue           []byte   `protobuf:"bytes,12,opt,name=tableValue" json:"tableValue,omitempty"`
	UnknowValue18        []byte   `protobuf:"bytes,18,opt,name=unknowValue18" json:"unknowValue18,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (x *SaeInfo) Reset() {
	*x = SaeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_copy_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaeInfo) ProtoMessage() {}

func (x *SaeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_copy_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaeInfo.ProtoReflect.Descriptor instead.
func (*SaeInfo) Descriptor() ([]byte, []int) {
	return file_copy_proto_rawDescGZIP(), []int{0}
}

func (x *SaeInfo) GetType() []byte {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *SaeInfo) GetIv() []byte {
	if x != nil {
		return x.Iv
	}
	return nil
}

func (x *SaeInfo) GetLen() uint32 {
	if x != nil && x.Len != nil {
		return *x.Len
	}
	return 0
}

func (x *SaeInfo) GetUnknowValue9() []byte {
	if x != nil {
		return x.UnknowValue9
	}
	return nil
}

func (x *SaeInfo) GetTableKey() []byte {
	if x != nil {
		return x.TableKey
	}
	return nil
}

func (x *SaeInfo) GetUnknowValue11() []byte {
	if x != nil {
		return x.UnknowValue11
	}
	return nil
}

func (x *SaeInfo) GetTableValue() []byte {
	if x != nil {
		return x.TableValue
	}
	return nil
}

func (x *SaeInfo) GetUnknowValue18() []byte {
	if x != nil {
		return x.UnknowValue18
	}
	return nil
}

var File_copy_proto protoreflect.FileDescriptor

var file_copy_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x63, 0x6f, 0x70, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04, 0x63, 0x6f,
	0x70, 0x79, 0x22, 0xfc, 0x02, 0x0a, 0x07, 0x53, 0x61, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x17,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x76, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0c, 0x48, 0x01, 0x52, 0x02, 0x69, 0x76, 0x88, 0x01, 0x01, 0x12, 0x15, 0x0a, 0x03,
	0x6c, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x48, 0x02, 0x52, 0x03, 0x6c, 0x65, 0x6e,
	0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0c, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x39, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x03, 0x52, 0x0c, 0x75, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x39, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x4b, 0x65, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x04,
	0x52, 0x08, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4b, 0x65, 0x79, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a,
	0x0d, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x31, 0x31, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x0c, 0x48, 0x05, 0x52, 0x0d, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x31, 0x31, 0x88, 0x01, 0x01, 0x12, 0x23, 0x0a, 0x0a, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x06, 0x52, 0x0a,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x12, 0x29, 0x0a,
	0x0d, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x31, 0x38, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x0c, 0x48, 0x07, 0x52, 0x0d, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x31, 0x38, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x69, 0x76, 0x42, 0x06, 0x0a, 0x04, 0x5f, 0x6c, 0x65, 0x6e,
	0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x39, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x4b, 0x65, 0x79, 0x42, 0x10,
	0x0a, 0x0e, 0x5f, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x31, 0x31,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42,
	0x10, 0x0a, 0x0e, 0x5f, 0x75, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x31,
	0x38, 0x42, 0x08, 0x5a, 0x06, 0x2f, 0x3b, 0x63, 0x6f, 0x70, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_copy_proto_rawDescOnce sync.Once
	file_copy_proto_rawDescData = file_copy_proto_rawDesc
)

func file_copy_proto_rawDescGZIP() []byte {
	file_copy_proto_rawDescOnce.Do(func() {
		file_copy_proto_rawDescData = protoimpl.X.CompressGZIP(file_copy_proto_rawDescData)
	})
	return file_copy_proto_rawDescData
}

var file_copy_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_copy_proto_goTypes = []interface{}{
	(*SaeInfo)(nil), // 0: copy.SaeInfo
}
var file_copy_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_copy_proto_init() }
func file_copy_proto_init() {
	if File_copy_proto != nil {
		return
	}
	file_copy_proto_msgTypes[0].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_copy_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_copy_proto_goTypes,
		DependencyIndexes: file_copy_proto_depIdxs,
		MessageInfos:      file_copy_proto_msgTypes,
	}.Build()
	File_copy_proto = out.File
	file_copy_proto_rawDesc = nil
	file_copy_proto_goTypes = nil
	file_copy_proto_depIdxs = nil
}
