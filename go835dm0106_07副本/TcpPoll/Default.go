//go:build !linux
// +build !linux

package TcpPoll

import (
	"errors"
	"wechatdll/comm"
)

// BusinessHeartBeatStarter 业务心跳启动器（避免循环导入）
var BusinessHeartBeatStarter func(wxid string) error

type TcpManager struct {
	running bool // 控制是否消息loop
}

type TcpClient struct {
	model     *comm.LoginData // wx缓存
	startTime int64           // tcp 开启时间
	running   bool            // 控制是否消息loop
}

func GetTcpManager() (*TcpManager, error) {
	return nil, errors.New("非Linux系统不支持长连接")
}

func (manager *TcpManager) Add(key string, client *TcpClient) error {
	return errors.New("非Linux系统不支持长连接")
}

func (manager *TcpManager) Remove(client *TcpClient) {
	return
}

func (manager *TcpManager) GetClient(loginData *comm.LoginData) (*TcpClient, error) {
	return nil, errors.New("非Linux系统不支持长连接")
}

func (manager *TcpManager) GetExistingClient(wxid string) *TcpClient {
	return nil
}

func (manager *TcpManager) RunEventLoop() {
	return
}

func (client *TcpClient) MmtlsSend(data []byte, cmdId int, tag string) (*[]byte, error) {
	return nil, errors.New("非Linux系统不支持长连接")
}

func (client *TcpClient) SetStartTime(time int64) {
	return
}

func (client *TcpClient) Terminate() {
	return
}

func (client *TcpClient) Send(data []byte, tag string) error {
	return errors.New("非Linux系统不支持长连接")
}

func (client *TcpClient) PackMmtlsLong(data []byte) ([]byte, error) {
	return nil, errors.New("非Linux系统不支持长连接")
}

func (client *TcpClient) SendTcpHeartBeat() {
	return
}

func (client *TcpClient) StartBusinessHeartBeat() {
	return
}

func NewTcpClient(model *comm.LoginData) *TcpClient {
	return &TcpClient{
		model:     model,
		startTime: 0,
		running:   false,
	}
}
