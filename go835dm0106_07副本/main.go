package main

import (
	"fmt"
	"log"
	"runtime"
	"wechatdll/TcpPoll"
	"wechatdll/comm"
	_ "wechatdll/routers"
	"wechatdll/srv/wxcore"

	"github.com/astaxie/beego"
)

func main() {
	// 增加全局recover处理
	defer func() {
		if r := recover(); r != nil {
			log.Printf("捕获到panic: %v", r)
		}
	}()

	longLinkEnabled, _ := beego.AppConfig.Bool("longlinkenabled")

	comm.RedisInitialize()
	_, err := comm.RedisClient.Ping().Result()
	if err != nil {
		log.Fatalf("【Redis】连接失败，ERROR：%v", err)
	}
	fmt.Println("By 猛牛科技")

	sysType := runtime.GOOS

	if longLinkEnabled {
		// 尝试启动TCP管理器
		tcpManager, err := TcpPoll.GetTcpManager()
		if err != nil {
			log.Printf("TCP启动失败: %v (系统: %s)", err, sysType)
		} else {
			go tcpManager.RunEventLoop()
			log.Printf("TCP管理器已启动 (系统: %s)", sysType)
		}
	}

	beego.BConfig.WebConfig.DirectoryIndex = true
	beego.BConfig.WebConfig.StaticDir["/"] = "swagger"

	beego.BeeLogger.SetLogger("file", `{"filename":"app.log"}`)

	// 初始化微信连接管理器（启用自动心跳和刷新功能）
	wxcore.InitWXConnectMgr()

	// 启动
	beego.Run()
}
