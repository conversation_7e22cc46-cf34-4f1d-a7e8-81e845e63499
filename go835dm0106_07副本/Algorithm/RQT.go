package Algorithm

import (
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
)

func RQT(data []byte) int {

	salt1 := [16]byte{0x5c, 0x50, 0x7b, 0x6b, 0x65, 0x4a, 0x13, 0x09, 0x45, 0x58, 0x7e, 0x11, 0x0c, 0x1f, 0x68, 0x79}
	salt2 := [16]byte{0x36, 0x3a, 0x11, 0x01, 0x0f, 0x20, 0x79, 0x63, 0x2f, 0x32, 0x14, 0x7b, 0x66, 0x75, 0x02, 0x13}

	pad1 := [0x30]byte{
		0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36,
		0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36,
		0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36, 0x36,
	}

	pad2 := [0x30]byte{
		0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c,
		0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c,
		0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c, 0x5c,
	}

	md5hash := md5.Sum(data)
	hashstr := hex.EncodeToString(md5hash[:])

	hash1Data := make([]byte, 16)
	copy(hash1Data, salt1[:])
	hash1Data = append(hash1Data, pad1[:]...)

	hash1 := sha1.New()
	hash1.Write(hash1Data)
	hash1.Write([]byte(hashstr))
	h1 := hash1.Sum(nil)

	hash2Data := make([]byte, 16)
	copy(hash2Data, salt2[:])
	hash2Data = append(hash2Data, pad2[:]...)

	hash2 := sha1.New()
	hash2.Write(hash2Data)
	hash2.Write(h1)
	h2 := hash2.Sum(nil)

	var b1, b2, b3 byte
	size := len(h2)

	for i := 0; i < size-2; i++ {
		b1 = h2[i+0] - b1*0x7d
		b2 = h2[i+1] - b2*0x7d
		b3 = h2[i+2] - b3*0x7d
	}

	return (int(0x21) << 24) | ((int(b3) & 0x7f) << 16) | ((int(b2) & 0x7f) << 8) | (int(b1) & 0x7f)
}

func RqtIOS(srcdata []byte) int {
	h := md5.New()
	h.Write(srcdata)
	md5sign := hex.EncodeToString(h.Sum(nil))
	key, _ := hex.DecodeString("6a664d5d537c253f736e48273a295e4f")
	mac := hmac.New(sha1.New, key)
	mac.Write([]byte(md5sign))
	my_sign := string(mac.Sum(nil))
	randvalue := 1
	index := 0
	temp0 := 0
	temp1 := 0
	temp2 := 0
	for index = 0; index+2 < 20; index++ {
		temp0 = (temp0&0xff)*0x83 + int(my_sign[index])
		temp1 = (temp1&0xff)*0x83 + int(my_sign[index+1])
		temp2 = (temp2&0xff)*0x83 + int(my_sign[index+2])

	}
	result := (temp2<<16)&0x7f0000 | temp0&0x7f | (randvalue&0x1f|0x20)<<24 | ((temp1 & 0x7f) << 8)
	return result

}
