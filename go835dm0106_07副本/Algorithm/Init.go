package Algorithm

import (
	"crypto/elliptic"
	"crypto/sha256"
	"encoding/hex"
)

func (h *Client) Init(Model string) {

	h.curve = elliptic.P256()
	h.clientHash = sha256.New()
	h.serverHash = sha256.New()

	if Model == "IOS" {
		h.Privkey, h.<PERSON> = GetECDH415Key()
		h.Version = IPadVersion
		h.DeviceType = IPadDeviceType
		h.InitPubKey, _ = hex.DecodeString("047ebe7604acf072b0ab0177ea551a7b72588f9b5d3801dfd7bb1bca8e33d1c3b8fa6e4e4026eb38d5bb365088a3d3167c83bdd0bbb46255f88a16ede6f7ab43b5")
	}

	if Model == "Android" {
		h.Status = HYBRID_ENC
		h.Version = AndroidVersion
		h.DeviceType = AndroidDeviceType
		h.InitPubKey, _ = hex.DecodeString("0495BC6E5C1331AD172D0F35B1792C3CE63F91572ABD2DD6DF6DAC2D70195C3F6627CCA60307305D8495A8C38B4416C75021E823B6C97DFFE79C14CB7C3AF8A586")
	}

}
func (h *Client) Init2(Model string) {

	h.curve = elliptic.P256()
	h.clientHash = sha256.New()
	h.serverHash = sha256.New()

	if Model == "IOS" {
		h.<PERSON>riv<PERSON>, h.<PERSON> = GetECDH415Key()
		h.Version = IPadVersion
		h.DeviceType = IPadDeviceType
		h.InitPubKey, _ = hex.DecodeString("044bb81879aff459ca8f1db3d38eea5d789afaed14765a859a6f70bf06b663f37c6bd9e05c9f5def4ab796ca2c45b9d9a0f553ac8be51c0f60e087faee24d14510")

	}

	if Model == "Android" {
		h.Status = HYBRID_ENC
		h.Version = AndroidVersion
		h.DeviceType = "android-28"
		h.InitPubKey, _ = hex.DecodeString("0495bc6e5c1331ad172d0f35b1792c3ce63f91572abd2dd6df6dac2d70195c3f6627cca60307305d8495a8c38b4416c75021e823b6c97dffe79c14cb7c3af8a586")
	}

}
func (h *Client) InitPlus(Model string, version int, deviceType string) {
	h.curve = elliptic.P256()
	h.clientHash = sha256.New()
	h.serverHash = sha256.New()
	if Model == "IOS" {
		h.Privkey, h.PubKey = GetECDH415Key()
		h.Version = version
		h.DeviceType = deviceType
		h.InitPubKey, _ = hex.DecodeString("047ebe7604acf072b0ab0177ea551a7b72588f9b5d3801dfd7bb1bca8e33d1c3b8fa6e4e4026eb38d5bb365088a3d3167c83bdd0bbb46255f88a16ede6f7ab43b5")
	}
	if Model == "Android" {
		h.Status = HYBRID_ENC
		h.Version = version
		h.DeviceType = deviceType
		h.InitPubKey, _ = hex.DecodeString("0495BC6E5C1331AD172D0F35B1792C3CE63F91572ABD2DD6DF6DAC2D70195C3F6627CCA60307305D8495A8C38B4416C75021E823B6C97DFFE79C14CB7C3AF8A586")
	}

}
